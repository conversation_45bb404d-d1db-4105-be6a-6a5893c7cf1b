-- =====================================================
-- FastAdmin 后台菜单添加脚本
-- 添加帖子管理相关菜单
-- =====================================================

-- 获取当前最大ID，避免冲突
SET @max_id = (SELECT MAX(id) FROM fa_auth_rule);

-- 添加主菜单：帖子管理
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 1, 'file', 0, 'posts', '帖子管理', 'fa fa-comments', '', '帖子管理模块', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal');

-- 添加子菜单：帖子列表
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 2, 'file', @max_id + 1, 'posts/index', '帖子列表', 'fa fa-list', '', '查看帖子列表', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 'normal');

-- 添加子菜单：添加帖子
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 3, 'file', @max_id + 1, 'posts/add', '添加帖子', 'fa fa-plus', '', '添加新帖子', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 'normal');

-- 添加子菜单：编辑帖子（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 4, 'file', @max_id + 1, 'posts/edit', '编辑帖子', 'fa fa-edit', '', '编辑帖子', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 'normal');

-- 添加子菜单：删除帖子（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 5, 'file', @max_id + 1, 'posts/del', '删除帖子', 'fa fa-trash', '', '删除帖子', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 'normal');

-- 添加子菜单：批量操作（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 6, 'file', @max_id + 1, 'posts/multi', '批量操作', 'fa fa-list', '', '批量操作帖子', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 5, 'normal');

-- 添加子菜单：上传图标（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 7, 'file', @max_id + 1, 'posts/uploadicon', '上传图标', 'fa fa-upload', '', '上传帖子图标', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, 'normal');

-- 添加主菜单：帖子分类
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 8, 'file', @max_id + 1, 'postcategories', '帖子分类', 'fa fa-tags', '', '帖子分类管理', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10, 'normal');

-- 添加分类子菜单：分类列表（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 9, 'file', @max_id + 8, 'postcategories/index', '分类列表', 'fa fa-list', '', '查看分类列表', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 'normal');

-- 添加分类子菜单：添加分类（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 10, 'file', @max_id + 8, 'postcategories/add', '添加分类', 'fa fa-plus', '', '添加新分类', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 'normal');

-- 添加分类子菜单：编辑分类（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 11, 'file', @max_id + 8, 'postcategories/edit', '编辑分类', 'fa fa-edit', '', '编辑分类', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 'normal');

-- 添加分类子菜单：删除分类（不显示在菜单中）
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@max_id + 12, 'file', @max_id + 8, 'postcategories/del', '删除分类', 'fa fa-trash', '', '删除分类', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 'normal');

-- 为超级管理员组添加权限（假设管理员组ID为1）
SET @admin_group_id = 1;
SET @new_rules = '';

-- 获取新添加的规则ID
SELECT GROUP_CONCAT(id SEPARATOR ',') INTO @new_rules 
FROM fa_auth_rule 
WHERE id > @max_id;

-- 更新管理员组权限
UPDATE fa_auth_group 
SET rules = CONCAT(rules, ',', @new_rules) 
WHERE id = @admin_group_id;

-- 执行完成提示
SELECT '✅ 后台菜单添加完成！' as '执行结果';
SELECT '📋 已添加以下菜单：' as '菜单列表';
SELECT '1. 帖子管理 (主菜单)' as '菜单项';
SELECT '2. ├── 帖子列表' as '菜单项';
SELECT '3. ├── 添加帖子' as '菜单项';
SELECT '4. └── 帖子分类' as '菜单项';
SELECT '🔑 权限已分配给超级管理员组' as '权限说明';
