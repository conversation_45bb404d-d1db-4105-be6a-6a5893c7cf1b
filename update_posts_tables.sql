-- =====================================================
-- 更新帖子功能数据库表结构
-- 修复时间字段类型以匹配FastAdmin规范
-- =====================================================

-- 如果表已存在，先删除重建
DROP TABLE IF EXISTS `fa_posts`;
DROP TABLE IF EXISTS `fa_post_categories`;

-- 重新创建帖子分类表
CREATE TABLE `fa_post_categories` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子分类表';

-- 重新创建帖子表
CREATE TABLE `fa_posts` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '帖子ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '分类ID',
  `title` varchar(255) NOT NULL COMMENT '帖子标题',
  `content` longtext NOT NULL COMMENT '帖子内容',
  `post_type` varchar(50) DEFAULT 'normal' COMMENT '帖子类型：normal普通,sale出售,hot热门,expert高手',
  `icon_type` varchar(20) DEFAULT 'builtin' COMMENT '图标类型：builtin内置,upload上传',
  `icon_path` varchar(255) DEFAULT NULL COMMENT '图标路径',
  `views` int(10) unsigned DEFAULT '0' COMMENT '浏览次数',
  `replies` int(10) unsigned DEFAULT '0' COMMENT '回复数',
  `likes` int(10) unsigned DEFAULT '0' COMMENT '点赞数',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶：1是，0否',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门：1是，0否',
  `status` varchar(20) DEFAULT 'published' COMMENT '状态：draft草稿,published已发布,hidden隐藏,deleted已删除',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_post_type` (`post_type`),
  KEY `idx_status` (`status`),
  KEY `idx_createtime` (`createtime`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_is_hot` (`is_hot`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子表';

-- 插入默认分类数据
INSERT INTO `fa_post_categories` (`id`, `name`, `description`, `sort_order`, `status`, `createtime`, `updatetime`) VALUES
(1, '综合讨论', '综合性话题讨论区', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '技术交流', '技术相关问题交流', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '买卖交易', '物品买卖交易区', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '新手求助', '新手问题求助区', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, '经验分享', '经验心得分享区', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(6, '公告通知', '官方公告通知区', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 创建示例帖子数据
INSERT INTO `fa_posts` (`id`, `user_id`, `category_id`, `title`, `content`, `post_type`, `icon_type`, `views`, `replies`, `likes`, `is_top`, `status`, `createtime`, `updatetime`) VALUES
(1, 1, 1, '欢迎来到论坛！', '<p>欢迎大家来到我们的论坛！这里是一个交流学习的好地方。</p><p>请大家遵守论坛规则，文明发言，共同维护良好的讨论环境。</p>', 'normal', 'builtin', 100, 5, 10, 1, 'published', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 1, 2, '技术问题求助', '<p>遇到技术问题不要慌，这里有很多热心的朋友会帮助你。</p><p>发帖时请详细描述问题，提供相关代码或截图。</p>', 'expert', 'builtin', 50, 3, 5, 0, 'published', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 1, 3, '二手物品交易须知', '<p>在交易区发布买卖信息时，请注意以下几点：</p><ul><li>详细描述物品状况</li><li>提供清晰的图片</li><li>标明价格和交易方式</li><li>注意交易安全</li></ul>', 'sale', 'builtin', 80, 2, 8, 0, 'published', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 执行完成提示
SELECT '✅ 数据库表结构更新完成！' as '执行结果';
SELECT '🔧 已修复时间字段类型问题' as '修复内容';
SELECT '📋 时间字段已改为int类型时间戳' as '字段说明';
