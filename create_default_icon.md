# 创建默认图标说明

## 📁 需要创建的图标文件

根据你的分类数量，需要在 `public/images/icons/` 目录下创建以下图标文件：

### 默认图标
- `default.png` - 默认图标（当其他图标不存在时显示）

### 分类图标（根据分类数量）
如果你有6个分类，需要创建：
- `category_1.png` - 对应"综合讨论"分类
- `category_2.png` - 对应"技术交流"分类  
- `category_3.png` - 对应"买卖交易"分类
- `category_4.png` - 对应"新手求助"分类
- `category_5.png` - 对应"经验分享"分类
- `category_6.png` - 对应"公告通知"分类

## 🎨 图标规格建议

- **尺寸**：32x32px 或 64x64px
- **格式**：PNG（支持透明背景）
- **风格**：简洁明了，符合分类主题

## 🔧 图标映射逻辑

现在的逻辑是：
1. 系统读取启用的分类数量
2. 为每个分类生成一个对应的图标选项
3. 图标文件按顺序命名：category_1.png, category_2.png...
4. 在后台显示时，图标下方显示对应的分类名称

## 📋 当前分类对应关系

根据数据库中的分类顺序：
1. category_1.png → 综合讨论
2. category_2.png → 技术交流
3. category_3.png → 买卖交易
4. category_4.png → 新手求助
5. category_5.png → 经验分享
6. category_6.png → 公告通知

## ⚠️ 注意事项

1. 如果图标文件不存在，会自动显示 `default.png`
2. 添加新分类时，需要相应添加新的图标文件
3. 删除分类时，对应的图标选项会自动消失
4. 图标文件名必须严格按照 `category_数字.png` 的格式命名
