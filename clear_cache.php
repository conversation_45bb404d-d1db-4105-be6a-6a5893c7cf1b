<?php
// 清理缓存脚本
echo "开始清理缓存...\n";

// 清理模板缓存
$templateCacheDir = 'runtime/temp/';
if (is_dir($templateCacheDir)) {
    $files = glob($templateCacheDir . '*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
            echo "删除模板缓存: " . $file . "\n";
        }
    }
}

// 清理日志缓存
$logCacheDir = 'runtime/log/';
if (is_dir($logCacheDir)) {
    $files = glob($logCacheDir . '*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
            echo "删除日志缓存: " . $file . "\n";
        }
    }
}

// 清理数据缓存
$dataCacheDir = 'runtime/cache/';
if (is_dir($dataCacheDir)) {
    $files = glob($dataCacheDir . '*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
            echo "删除数据缓存: " . $file . "\n";
        }
    }
}

echo "缓存清理完成！\n";
echo "请刷新页面重新测试编辑功能。\n";
?>
