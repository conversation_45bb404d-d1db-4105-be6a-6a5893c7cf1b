<?php
// 测试帖子编辑数据
// 访问: http://127.0.0.3/test_post_edit.php?id=1

require_once 'application/common.php';

use app\common\model\Post;

$id = $_GET['id'] ?? 1;

$post = Post::get($id);

if (!$post) {
    die("帖子不存在");
}

echo "<h2>帖子编辑数据测试</h2>";
echo "<p><strong>帖子ID:</strong> " . $post->id . "</p>";
echo "<p><strong>标题:</strong> " . $post->title . "</p>";
echo "<p><strong>原始post_type:</strong> " . $post->getData('post_type') . "</p>";
echo "<p><strong>获取器post_type:</strong> " . $post->post_type . "</p>";
echo "<p><strong>post_type_text:</strong> " . $post->post_type_text . "</p>";

echo "<h3>POST_TYPES常量:</h3>";
echo "<pre>" . print_r(Post::POST_TYPES, true) . "</pre>";

echo "<h3>完整数据:</h3>";
echo "<pre>" . print_r($post->toArray(), true) . "</pre>";

echo "<h3>模板条件测试:</h3>";
foreach (Post::POST_TYPES as $key => $name) {
    $selected = ($key == $post->post_type) ? 'SELECTED' : '';
    echo "<p>{$key} == {$post->post_type} ? {$selected} ({$name})</p>";
}
?>
