-- =====================================================
-- 修复帖子类型问题
-- 将空的或NULL的post_type设置为默认值
-- =====================================================

-- 检查当前的post_type值
SELECT '修复前的数据:' as '状态';
SELECT id, title, post_type, icon_type FROM fa_posts;

-- 修复空的post_type
UPDATE fa_posts SET post_type = 'normal' WHERE post_type IS NULL OR post_type = '';

-- 修复空的icon_type
UPDATE fa_posts SET icon_type = 'category' WHERE icon_type IS NULL OR icon_type = '';

-- 检查修复后的数据
SELECT '修复后的数据:' as '状态';
SELECT id, title, post_type, icon_type FROM fa_posts;

-- 执行完成提示
SELECT '✅ 帖子类型修复完成！' as '执行结果';
SELECT '📋 所有空的post_type已设置为normal' as '修复说明';
SELECT '📋 所有空的icon_type已设置为category' as '修复说明';
