<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" data-rule="required">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">分类:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-category_id" class="form-control selectpicker" name="row[category_id]" data-rule="required">
                <option value="">请选择分类</option>
                {volist name="categoryList" id="category"}
                <option value="{$category.id}" {if condition="$category.id == $row.category_id"}selected{/if}>{$category.name}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">帖子类型:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-post_type" class="form-control selectpicker" name="row[post_type]">
                {volist name="postTypes" id="typeName" key="typeKey"}
                <option value="{$typeKey}" {if condition="$typeKey == $row.post_type"}selected{/if}>{$typeName}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">图标类型:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[icon_type]" value="builtin" {if condition="$row.icon_type == 'builtin' || !$row.icon_type"}checked{/if}> 内置图标</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[icon_type]" value="upload" {if condition="$row.icon_type == 'upload'"}checked{/if}> 上传图标</label>
            </div>
        </div>
    </div>
    <div class="form-group" id="builtin-icons" {if condition="$row.icon_type == 'upload'"}style="display: none;"{/if}>
        <label class="control-label col-xs-12 col-sm-2">选择内置图标:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="icon-grid">
                {volist name="builtinIcons" id="iconPath" key="iconKey"}
                <div class="icon-item" data-value="{$iconKey}">
                    <img src="{$iconPath}" alt="{$iconKey}" style="width: 32px; height: 32px;">
                    <div>{$iconKey}</div>
                </div>
                {/volist}
            </div>
        </div>
    </div>
    <div class="form-group" id="upload-icon" {if condition="$row.icon_type != 'upload'"}style="display: none;"{/if}>
        <label class="control-label col-xs-12 col-sm-2">上传图标:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon_path" class="form-control" size="50" name="row[icon_path]" type="text" value="{$row.icon_path|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-icon" class="btn btn-danger plupload" data-input-id="c-icon_path" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-icon"><i class="fa fa-upload"></i> 上传</button></span>
                </div>
                <span class="msg-box n-right" for="c-icon_path"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-icon"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50" data-rule="required">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">置顶:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[is_top]" value="1" {if condition="$row.is_top == 1"}checked{/if}> 是</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[is_top]" value="0" {if condition="$row.is_top == 0"}checked{/if}> 否</label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">热门:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[is_hot]" value="1" {if condition="$row.is_hot == 1"}checked{/if}> 是</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[is_hot]" value="0" {if condition="$row.is_hot == 0"}checked{/if}> 否</label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">状态:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[status]" value="published" {if condition="$row.status == 'published'"}checked{/if}> 已发布</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[status]" value="draft" {if condition="$row.status == 'draft'"}checked{/if}> 草稿</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[status]" value="hidden" {if condition="$row.status == 'hidden'"}checked{/if}> 隐藏</label>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">确定</button>
            <button type="reset" class="btn btn-default btn-embossed">重置</button>
        </div>
    </div>
</form>

<style>
.icon-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.icon-item {
    text-align: center;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    min-width: 80px;
}
.icon-item:hover {
    border-color: #007bff;
}
.icon-item.selected {
    border-color: #007bff;
    background-color: #f0f8ff;
}
</style>

<script>
$(function() {
    // 图标类型切换
    $('input[name="row[icon_type]"]').change(function() {
        if ($(this).val() === 'builtin') {
            $('#builtin-icons').show();
            $('#upload-icon').hide();
        } else {
            $('#builtin-icons').hide();
            $('#upload-icon').show();
        }
    });
    
    // 内置图标选择
    $('.icon-item').click(function() {
        $('.icon-item').removeClass('selected');
        $(this).addClass('selected');
        var iconKey = $(this).data('value');
        $('input[name="row[selected_icon]"]').remove();
        $('<input>').attr({
            type: 'hidden',
            name: 'row[selected_icon]',
            value: iconKey
        }).appendTo('#edit-form');
    });
    
    // 根据当前帖子类型选中对应图标
    var currentPostType = '{$row.post_type}';
    if (currentPostType && $('input[name="row[icon_type]"]:checked').val() === 'builtin') {
        $('.icon-item[data-value="' + currentPostType + '"]').addClass('selected');
    }
});
</script>
