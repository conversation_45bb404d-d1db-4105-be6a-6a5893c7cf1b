<?php
// 调试帖子数据脚本
require_once 'application/common.php';

use app\common\model\Post;

// 获取所有帖子的原始数据
$posts = Post::select();

echo "=== 帖子数据调试 ===\n";
foreach ($posts as $post) {
    echo "ID: " . $post->id . "\n";
    echo "标题: " . $post->title . "\n";
    echo "原始post_type: " . $post->getData('post_type') . "\n";
    echo "获取器post_type: " . $post->post_type . "\n";
    echo "post_type_text: " . $post->post_type_text . "\n";
    echo "------------------------\n";
}

// 测试特定帖子
echo "\n=== 测试编辑帖子ID=1 ===\n";
$testPost = Post::get(1);
if ($testPost) {
    echo "原始数据: " . json_encode($testPost->getData()) . "\n";
    echo "post_type: " . $testPost->post_type . "\n";
    echo "post_type_text: " . $testPost->post_type_text . "\n";
} else {
    echo "帖子不存在\n";
}
?>
