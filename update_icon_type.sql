-- =====================================================
-- 更新帖子图标类型
-- 将所有帖子的图标类型改为使用分类图标
-- =====================================================

-- 更新现有帖子的图标类型为category
UPDATE `fa_posts` SET `icon_type` = 'category' WHERE `icon_type` = 'builtin' OR `icon_type` IS NULL;

-- 清空不需要的icon_path字段（对于使用分类图标的帖子）
UPDATE `fa_posts` SET `icon_path` = NULL WHERE `icon_type` = 'category';

-- 执行完成提示
SELECT '✅ 帖子图标类型更新完成！' as '执行结果';
SELECT '📋 所有帖子现在使用分类图标' as '更新说明';
