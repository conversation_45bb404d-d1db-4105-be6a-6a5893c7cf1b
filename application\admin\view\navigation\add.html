<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label for="c-title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-icon" class="control-label col-xs-12 col-sm-2">{:__('Icon')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon" class="form-control" name="row[icon]" type="text" value="fa fa-list">
                <span class="input-group-addon btn btn-primary btn-icon-picker"><i class="fa fa-list"></i></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-url" class="control-label col-xs-12 col-sm-2">{:__('Url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-url" data-rule="required" class="form-control" name="row[url]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-target" class="control-label col-xs-12 col-sm-2">{:__('Target')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-target" class="form-control selectpicker" name="row[target]">
                {foreach name="targetList" item="vo"}
                <option value="{$key|htmlentities}" {in name="key" value="_self"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="c-sort" class="control-label col-xs-12 col-sm-2">{:__('Sort')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
