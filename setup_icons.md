# 发帖功能图标设置说明

## 📁 需要创建的目录

请确保以下目录存在并有写入权限：

```
public/uploads/icons/     # 用户上传图标目录
public/uploads/posts/     # 帖子内容图片目录  
public/images/icons/      # 内置图标目录
```

## 🎨 内置图标文件

需要在 `public/images/icons/` 目录下放置以下图标文件：

### 帖子类型图标
- `normal.png` - 普通帖图标 (32x32px)
- `sale.png` - 出售帖图标 (32x32px) 
- `hot.png` - 热门帖图标 (32x32px)
- `expert.png` - 高手帖图标 (32x32px)

### 状态图标  
- `new.png` - 新帖图标 (32x32px)
- `top.png` - 置顶帖图标 (32x32px)

## 🚀 执行步骤

### 1. 执行数据库脚本
```bash
mysql -h127.0.0.1 -uroot -p661381abc houtai < posts_tables.sql
```

### 2. 创建上传目录
```bash
mkdir -p public/uploads/icons
mkdir -p public/uploads/posts  
mkdir -p public/images/icons
```

### 3. 设置目录权限（Linux/Mac）
```bash
chmod 755 public/uploads/icons
chmod 755 public/uploads/posts
chmod 755 public/images/icons
```

### 4. 上传内置图标
将对应的图标文件上传到 `public/images/icons/` 目录

## 📋 功能测试

执行完成后可以测试以下功能：

### 前端页面
- 发帖页面：`/index/posts/create`
- 帖子详情：`/index/posts/detail?id=1`

### API接口
- 帖子列表：`GET /api/posts/index`
- 帖子详情：`GET /api/posts/detail?id=1`
- 发布帖子：`POST /api/posts/create`
- 分类列表：`GET /api/posts/categories`
- 内置图标：`GET /api/posts/builtinIcons`
- 上传图标：`POST /api/posts/uploadIcon`

### 后台管理
- 帖子管理：`/admin/posts/index`
- 添加帖子：`/admin/posts/add`
- 编辑帖子：`/admin/posts/edit`

## 🎯 图标建议

### 图标规格
- 尺寸：32x32px 或 64x64px
- 格式：PNG（支持透明背景）
- 风格：简洁明了，符合主题

### 图标设计建议
- **普通帖**：文档或对话气泡图标
- **出售帖**：购物车或价格标签图标  
- **热门帖**：火焰或星星图标
- **高手帖**：皇冠或奖杯图标
- **新帖**：NEW标签或闪光图标
- **置顶帖**：图钉或向上箭头图标

## ⚠️ 注意事项

1. 确保数据库连接正常
2. 检查目录权限设置
3. 图标文件命名要与代码中定义的一致
4. 建议先在测试环境验证功能
5. 备份现有数据库（如有重要数据）

执行完成后，发帖功能就可以正常使用了！
