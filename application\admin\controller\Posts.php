<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\Post;
use app\common\model\PostCategory;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

/**
 * 帖子管理
 */
class Posts extends Backend
{
    /**
     * Post模型对象
     * @var \app\common\model\Post
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new Post;

        // 设置数据限制字段为user_id
        $this->dataLimit = 'auth';
        $this->dataLimitField = 'user_id';
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自定义
     * 如果需要自定义，请复制对应的方法至此文件中
     */

    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user', 'category'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','user_id','category_id','title','post_type','post_type_text','icon_url','views','replies','likes','is_top','is_hot','status','status_text','createtime']);
                $row->visible(['user']);
                $row->getRelation('user')->visible(['username','nickname']);
                $row->visible(['category']);
                $row->getRelation('category')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 获取分类列表
        $categoryList = PostCategory::getEnabledCategories();
        $this->view->assign("categoryList", $categoryList);
        $this->view->assign("postTypes", Post::POST_TYPES);
        $this->view->assign("builtinIcons", Post::getBuiltinIcons());
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 获取分类列表
        $categoryList = PostCategory::getEnabledCategories();
        $this->view->assign("categoryList", $categoryList);
        $this->view->assign("postTypes", Post::POST_TYPES);
        $this->view->assign("builtinIcons", Post::getBuiltinIcons());
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 上传图标
     */
    public function uploadIcon()
    {
        $file = $this->request->file('file');
        if (!$file) {
            $this->error('请选择文件');
        }

        // 验证文件
        $validate = [
            'size' => 2097152, // 2MB
            'ext' => 'jpg,jpeg,png,gif'
        ];

        $info = $file->validate($validate)->move(ROOT_PATH . 'public/uploads/icons');
        if ($info) {
            $filePath = '/uploads/icons/' . $info->getSaveName();
            $this->success('上传成功', ['url' => $filePath]);
        } else {
            $this->error($file->getError());
        }
    }
}
