<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
        }

        /* 主容器 */
        .profile-container {
            display: flex;
            min-height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        /* 汉堡菜单按钮 */
        .menu-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 2px 10px rgba(0,123,255,0.3);
        }

        .menu-toggle:hover {
            background: #0056b3;
        }

        /* 遮罩层 */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        /* 手机端适配 */
        @media (max-width: 768px) {
            .profile-container {
                flex-direction: row;
                max-width: 100%;
                margin: 0;
                box-shadow: none;
            }

            .menu-toggle {
                display: block;
            }
        }

        /* 左侧菜单 */
        .sidebar {
            width: 200px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            flex-shrink: 0;
        }

        /* 手机端左侧菜单 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
                border-right: 1px solid #dee2e6;
                border-bottom: none;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .sidebar.open {
                left: 0;
            }
        }

        /* 左上角标题 */
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            background: white;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        /* 菜单列表 */
        .menu-list {
            padding: 0;
        }

        .menu-item {
            display: block;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            color: #333;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        /* 手机端菜单项 */
        @media (max-width: 768px) {
            .menu-list {
                display: block;
                padding: 0;
                margin-top: 60px;
            }

            .menu-item {
                display: block;
                width: 100%;
                padding: 15px 20px;
                text-align: left;
                border: none;
                border-bottom: 1px solid #e9ecef;
                border-radius: 0;
                font-size: 14px;
                margin: 0;
            }
        }

        .menu-item:hover {
            background-color: #e9ecef;
            color: #007bff;
        }

        .menu-item.active {
            background-color: #007bff;
            color: white;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        /* 右侧内容区域 */
        .content-area {
            flex: 1;
            padding: 30px;
            background: white;
        }

        /* 手机端内容区域 */
        @media (max-width: 768px) {
            .content-area {
                width: 100%;
                padding: 70px 15px 15px 15px;
            }
        }

        .content-header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .content-subtitle {
            font-size: 14px;
            color: #666;
        }

        .content-body {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }

        /* 返回首页按钮 */
        .back-home {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
            z-index: 1000;
        }

        /* 手机端返回按钮 */
        @media (max-width: 768px) {
            .back-home {
                position: fixed;
                bottom: 20px;
                right: 20px;
                top: auto;
                padding: 8px 15px;
                font-size: 12px;
            }
        }

        .back-home:hover {
            background: #218838;
        }

        /* 子菜单标签 */
        .sub-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e9ecef;
            flex-wrap: wrap;
        }

        .sub-tab {
            padding: 12px 20px;
            margin-right: 10px;
            margin-bottom: 5px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 5px 5px 0 0;
        }

        /* 手机端子菜单标签 */
        @media (max-width: 768px) {
            .sub-tabs {
                margin-bottom: 20px;
                border-bottom: none;
                gap: 8px;
            }

            .sub-tab {
                flex: 1;
                min-width: calc(50% - 4px);
                padding: 12px 8px;
                margin-right: 0;
                margin-bottom: 0;
                font-size: 13px;
                text-align: center;
                border-radius: 8px;
                border: 2px solid #e9ecef;
                background: #f8f9fa;
                color: #666;
                font-weight: 500;
                transition: all 0.3s ease;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .sub-tab:hover {
                background: #e9ecef;
                border-color: #007bff;
                color: #007bff;
                transform: translateY(-1px);
                box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            }

            .sub-tab.active {
                background: #007bff;
                border-color: #007bff;
                color: white;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(0,123,255,0.3);
            }
        }

        .sub-tab:hover {
            background: #e9ecef;
            color: #333;
        }

        .sub-tab.active {
            background: white;
            color: #007bff;
            border-color: #007bff;
            border-bottom: 1px solid white;
            position: relative;
            z-index: 1;
        }

        .sub-tab:last-child {
            margin-right: 0;
        }
    </style>
</head>
<body>
    <!-- 汉堡菜单按钮 -->
    <button class="menu-toggle" onclick="toggleSidebar()">
        <span>☰</span>
    </button>

    <!-- 遮罩层 -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- 主容器 -->
    <div class="profile-container">
        <!-- 左侧菜单 -->
        <div class="sidebar">
            <!-- 左上角标题 -->
            <div class="sidebar-header">
                <div class="sidebar-title">设置中心</div>
                <div id="userInfo" style="font-size: 12px; color: #666; margin-top: 5px;">
                    <span id="userStatus">检查登录状态...</span>
                </div>
            </div>

            <!-- 菜单列表 -->
            <div class="menu-list">
                <a href="javascript:void(0)" class="menu-item active" onclick="showContent('edit-profile', this)">编辑资料</a>
                <a href="javascript:void(0)" class="menu-item" onclick="showContent('forum-settings', this)">论坛设置</a>
                <a href="javascript:void(0)" class="menu-item" onclick="showContent('user-permissions', this)">用户权限</a>
                <a href="javascript:void(0)" class="menu-item" onclick="showContent('points-management', this)">积分管理</a>
                <a href="javascript:void(0)" class="menu-item" onclick="showContent('points-recharge', this)">积分充值</a>
                <a href="javascript:void(0)" class="menu-item" onclick="showContent('points-convert', this)">积分转换</a>
                <a href="javascript:void(0)" class="menu-item" onclick="showContent('special-group', this)">特殊组购买</a>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="content-area">
            <div class="content-header">
                <div class="content-title" id="contentTitle">编辑资料</div>
                <div class="content-subtitle" id="contentSubtitle">管理您的个人信息和账户设置</div>
            </div>
            <div class="content-body" id="contentBody">
                <!-- 编辑资料的子菜单标签 -->
                <div class="sub-tabs" id="subTabs" style="display: block;">
                    <a href="javascript:void(0)" class="sub-tab active" onclick="showSubContent('basic-info', this)">基本资料</a>
                    <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('trade-info', this)">交易信息</a>
                    <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('contact-info', this)">联系方式</a>
                    <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('avatar', this)">修改头像</a>
                    <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('password', this)">密码安全</a>
                </div>

                <!-- 子内容区域 -->
                <div id="subContentArea">
                    <p>基本资料管理功能开发中...</p>
                    <p>您可以在这里修改用户名、昵称、个人简介等基本信息。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回首页 -->
    <a href="/" class="back-home">返回首页</a>

    <script>
        // 检查登录状态
        function checkLoginStatus() {
            const userToken = localStorage.getItem('userToken');
            const userInfo = localStorage.getItem('userInfo');

            if (!userToken || !userInfo) {
                // 未登录，跳转到登录页面
                alert('请先登录');
                window.location.href = '/index/index/login';
                return false;
            }

            try {
                const user = JSON.parse(userInfo);
                document.getElementById('userStatus').textContent = `欢迎，${user.username || user.nickname || '用户'}`;
                return true;
            } catch (error) {
                console.error('用户信息解析失败:', error);
                localStorage.removeItem('userToken');
                localStorage.removeItem('userInfo');
                alert('登录信息异常，请重新登录');
                window.location.href = '/index/index/login';
                return false;
            }
        }

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 切换侧边栏显示/隐藏
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.toggle('open');
            if (sidebar.classList.contains('open')) {
                overlay.style.display = 'block';
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            } else {
                overlay.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 关闭侧边栏
        function closeSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.remove('open');
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showContent(pageType, element) {
            // 移除所有菜单项的active类
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 给当前点击的菜单项添加active类
            element.classList.add('active');

            // 在手机端点击菜单项后关闭侧边栏
            if (window.innerWidth <= 768) {
                closeSidebar();
            }

            // 更新右侧内容
            const contentTitle = document.getElementById('contentTitle');
            const contentSubtitle = document.getElementById('contentSubtitle');
            const contentBody = document.getElementById('contentBody');

            switch(pageType) {
                case 'edit-profile':
                    contentTitle.textContent = '编辑资料';
                    contentSubtitle.textContent = '管理您的个人信息和账户设置';
                    contentBody.innerHTML = `
                        <!-- 编辑资料的子菜单标签 -->
                        <div class="sub-tabs" id="subTabs">
                            <a href="javascript:void(0)" class="sub-tab active" onclick="showSubContent('basic-info', this)">基本资料</a>
                            <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('trade-info', this)">交易信息</a>
                            <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('contact-info', this)">联系方式</a>
                            <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('avatar', this)">修改头像</a>
                            <a href="javascript:void(0)" class="sub-tab" onclick="showSubContent('password', this)">密码安全</a>
                        </div>

                        <!-- 子内容区域 -->
                        <div id="subContentArea">
                            <p>基本资料管理功能开发中...</p>
                            <p>您可以在这里修改用户名、昵称、个人简介等基本信息。</p>
                        </div>
                    `;
                    break;
                case 'forum-settings':
                    contentTitle.textContent = '论坛设置';
                    contentSubtitle.textContent = '自定义您的论坛使用体验';
                    contentBody.innerHTML = '<p>论坛设置功能开发中...</p><p>您可以在这里设置论坛显示偏好、通知设置等。</p>';
                    break;
                case 'user-permissions':
                    contentTitle.textContent = '用户权限';
                    contentSubtitle.textContent = '查看您的账户权限和等级信息';
                    contentBody.innerHTML = '<p>用户权限功能开发中...</p><p>您可以在这里查看您的用户等级、权限范围等信息。</p>';
                    break;
                case 'points-management':
                    contentTitle.textContent = '积分管理';
                    contentSubtitle.textContent = '查看和管理您的积分账户';
                    contentBody.innerHTML = '<p>积分管理功能开发中...</p><p>您可以在这里查看积分余额、积分历史记录等。</p>';
                    break;
                case 'points-recharge':
                    contentTitle.textContent = '积分充值';
                    contentSubtitle.textContent = '为您的账户充值积分';
                    contentBody.innerHTML = '<p>积分充值功能开发中...</p><p>您可以在这里选择充值套餐，为账户充值积分。</p>';
                    break;
                case 'points-convert':
                    contentTitle.textContent = '积分转换';
                    contentSubtitle.textContent = '积分兑换和转换服务';
                    contentBody.innerHTML = '<p>积分转换功能开发中...</p><p>您可以在这里进行积分兑换、转换等操作。</p>';
                    break;
                case 'special-group':
                    contentTitle.textContent = '特殊组购买';
                    contentSubtitle.textContent = '购买特殊用户组获得更多权限';
                    contentBody.innerHTML = '<p>特殊组购买功能开发中...</p><p>您可以在这里购买VIP、高级会员等特殊用户组。</p>';
                    break;
                default:
                    contentTitle.textContent = '功能开发中';
                    contentSubtitle.textContent = '该功能正在开发中，敬请期待';
                    contentBody.innerHTML = '<p>功能开发中...</p>';
            }
        }

        function showSubContent(subType, element) {
            // 移除所有子标签的active类
            document.querySelectorAll('.sub-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 给当前点击的子标签添加active类
            element.classList.add('active');

            // 更新子内容区域
            const subContentArea = document.getElementById('subContentArea');

            switch(subType) {
                case 'basic-info':
                    subContentArea.innerHTML = `
                        <p>基本资料管理功能开发中...</p>
                        <p>您可以在这里修改用户名、昵称、个人简介等基本信息。</p>
                    `;
                    break;
                case 'trade-info':
                    subContentArea.innerHTML = `
                        <p>交易信息管理功能开发中...</p>
                        <p>您可以在这里设置支付方式、交易偏好等信息。</p>
                    `;
                    break;
                case 'contact-info':
                    subContentArea.innerHTML = `
                        <p>联系方式管理功能开发中...</p>
                        <p>您可以在这里修改邮箱、手机号码等联系方式。</p>
                    `;
                    break;
                case 'avatar':
                    subContentArea.innerHTML = `
                        <p>修改头像功能开发中...</p>
                        <p>您可以在这里上传和更换您的头像图片。</p>
                    `;
                    break;
                case 'password':
                    subContentArea.innerHTML = `
                        <p>密码安全管理功能开发中...</p>
                        <p>您可以在这里修改登录密码、设置安全问题等。</p>
                    `;
                    break;
                default:
                    subContentArea.innerHTML = '<p>功能开发中...</p>';
            }
        }
    </script>
</body>
</html>
