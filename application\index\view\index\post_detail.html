<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$site.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #00d1ff;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }

        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
            color: white;
            font-size: 14px;
        }

        /* 内容区域 */
        .content-area {
            padding: 20px 15px;
        }

        /* 帖子头部信息 */
        .post-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .post-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .post-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }

        .post-type {
            background: #00d1ff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
        }

        .post-type.sale {
            background: #ff6b6b;
        }

        .post-type.hot {
            background: #ff9500;
        }

        .post-type.expert {
            background: #9c27b0;
        }

        .author-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }

        .author-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
        }

        .author-name {
            font-size: 13px;
            color: #666;
        }

        /* 帖子内容 */
        .post-content {
            line-height: 1.6;
            color: #333;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px 0;
        }

        .post-content p {
            margin-bottom: 10px;
        }

        /* 帖子统计 */
        .post-stats {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        /* 底部操作栏 */
        .bottom-actions {
            margin-top: 20px;
            padding: 15px 0;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 12px;
        }

        .action-btn:hover {
            color: #00d1ff;
        }

        .action-btn.active {
            color: #00d1ff;
        }

        .action-icon {
            font-size: 18px;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        .error {
            text-align: center;
            padding: 40px 20px;
            color: #ff6b6b;
        }

        /* 响应式调整 */
        @media (max-width: 375px) {
            .mobile-container, .top-nav, .bottom-actions {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <a href="javascript:history.back()" class="back-btn">← 返回</a>
            <div class="page-title">帖子详情</div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area" id="contentArea">
            <div class="loading">加载中...</div>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 加载帖子详情
        function loadPostDetail() {
            const postId = getUrlParam('id');
            if (!postId) {
                showError('参数错误');
                return;
            }

            fetch(`/api/posts/detail/${postId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data) {
                        renderPostDetail(data.data);
                    } else {
                        showError(data.msg || '帖子不存在');
                    }
                })
                .catch(error => {
                    console.error('加载帖子详情失败:', error);
                    showError('加载失败，请稍后重试');
                });
        }

        // 渲染帖子详情
        function renderPostDetail(post) {
            const contentArea = document.getElementById('contentArea');
            const postTypeClass = post.post_type || 'normal';
            const postTypeName = getPostTypeName(post.post_type);
            const createTime = formatTime(post.createtime);

            const html = `
                <div class="post-header">
                    <h1 class="post-title">${post.title}</h1>
                    <div class="post-meta">
                        <span class="post-type ${postTypeClass}">${postTypeName}</span>
                        <span>👁 ${post.views}</span>
                        <span>💬 ${post.replies}</span>
                        <span>👍 ${post.likes}</span>
                        <span>${createTime}</span>
                    </div>
                    <div class="author-info">
                        <img src="${post.user.avatar}" alt="头像" class="author-avatar">
                        <span class="author-name">${post.user.nickname || post.user.username}</span>
                    </div>
                </div>

                <div class="post-content">
                    ${post.content}
                </div>

                <div class="post-stats">
                    <div class="stat-item">
                        <span class="stat-number">${post.views}</span>
                        <span class="stat-label">浏览</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${post.replies}</span>
                        <span class="stat-label">回复</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${post.likes}</span>
                        <span class="stat-label">点赞</span>
                    </div>
                </div>

                <div class="bottom-actions">
                    <button class="action-btn" onclick="toggleLike()">
                        <span class="action-icon">👍</span>
                        <span>点赞</span>
                    </button>
                    <button class="action-btn" onclick="showReply()">
                        <span class="action-icon">💬</span>
                        <span>回复</span>
                    </button>
                    <button class="action-btn" onclick="sharePost()">
                        <span class="action-icon">📤</span>
                        <span>分享</span>
                    </button>
                </div>
            `;

            contentArea.innerHTML = html;
            
            // 更新页面标题
            document.title = post.title + ' - ' + (window.siteName || '网站');
        }

        // 显示错误信息
        function showError(message) {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = `<div class="error">${message}</div>`;
        }

        // 获取帖子类型名称
        function getPostTypeName(type) {
            const types = {
                'normal': '普通帖',
                'sale': '出售帖',
                'hot': '热门帖',
                'expert': '高手帖'
            };
            return types[type] || '普通帖';
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp * 1000);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) {
                return '刚刚';
            } else if (diff < 3600000) {
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) {
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 点赞功能
        function toggleLike() {
            // TODO: 实现点赞功能
            console.log('点赞功能待实现');
        }

        // 回复功能
        function showReply() {
            // TODO: 实现回复功能
            console.log('回复功能待实现');
        }

        // 分享功能
        function sharePost() {
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadPostDetail();
        });
    </script>
</body>
</html>
