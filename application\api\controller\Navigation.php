<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Navigation as NavigationModel;

/**
 * 导航链接接口
 */
class Navigation extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 获取导航列表
     */
    public function index()
    {
        $list = NavigationModel::getActiveList();
        
        $result = [];
        foreach ($list as $item) {
            $result[] = [
                'id' => $item['id'],
                'title' => $item['title'],
                'icon' => $item['icon'],
                'url' => $item['url'],
                'target' => $item['target'],
                'sort' => $item['sort']
            ];
        }
        
        $this->success('获取成功', $result);
    }

    /**
     * 获取单个导航详情
     */
    public function detail()
    {
        $id = $this->request->param('id');
        if (!$id) {
            $this->error('参数错误');
        }

        $navigation = NavigationModel::where('id', $id)
            ->where('status', 'normal')
            ->find();

        if (!$navigation) {
            $this->error('导航不存在');
        }

        $result = [
            'id' => $navigation['id'],
            'title' => $navigation['title'],
            'icon' => $navigation['icon'],
            'url' => $navigation['url'],
            'target' => $navigation['target'],
            'sort' => $navigation['sort']
        ];

        $this->success('获取成功', $result);
    }

    /**
     * 获取导航数量
     */
    public function count()
    {
        $count = NavigationModel::where('status', 'normal')->count();
        $this->success('获取成功', ['count' => $count]);
    }
}
