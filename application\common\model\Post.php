<?php

namespace app\common\model;

use think\Model;

/**
 * 帖子模型
 */
class Post extends Model
{

    // 表名
    protected $name = 'posts';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'post_type_text',
        'status_text',
        'icon_url'
    ];

    // 帖子类型
    const POST_TYPES = [
        'normal' => '普通帖',
        'sale' => '出售帖',
        'hot' => '热门帖',
        'expert' => '高手帖'
    ];

    // 帖子状态
    const STATUS_LIST = [
        'draft' => '草稿',
        'published' => '已发布',
        'hidden' => '隐藏',
        'deleted' => '已删除'
    ];

    // 内置图标
    const BUILTIN_ICONS = [
        'sale' => '/images/icons/sale.png',
        'hot' => '/images/icons/hot.png',
        'expert' => '/images/icons/expert.png',
        'normal' => '/images/icons/normal.png',
        'new' => '/images/icons/new.png',
        'top' => '/images/icons/top.png'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo('PostCategory', 'category_id');
    }

    /**
     * 获取帖子类型文本
     */
    public function getPostTypeTextAttr($value, $data)
    {
        return self::POST_TYPES[$data['post_type']] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::STATUS_LIST[$data['status']] ?? '未知';
    }

    /**
     * 获取图标URL
     */
    public function getIconUrlAttr($value, $data)
    {
        if ($data['icon_type'] == 'upload' && $data['icon_path']) {
            return $data['icon_path'];
        } elseif ($data['icon_type'] == 'builtin') {
            return self::BUILTIN_ICONS[$data['post_type']] ?? self::BUILTIN_ICONS['normal'];
        }
        return self::BUILTIN_ICONS['normal'];
    }

    /**
     * 搜索器：按标题搜索
     */
    public function searchTitleAttr($query, $value)
    {
        $query->where('title', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器：按类型搜索
     */
    public function searchPostTypeAttr($query, $value)
    {
        $query->where('post_type', $value);
    }

    /**
     * 搜索器：按状态搜索
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 增加浏览次数
     */
    public function incrementViews()
    {
        $this->setInc('views');
    }

    /**
     * 增加回复数
     */
    public function incrementReplies()
    {
        $this->setInc('replies');
    }

    /**
     * 增加点赞数
     */
    public function incrementLikes()
    {
        $this->setInc('likes');
    }
}
