# 导航链接功能使用说明

## 功能概述
导航链接功能为系统提供了完整的导航管理能力，支持设置图标、标题、链接地址、打开方式等属性，并提供了后台管理界面和API接口。

## 文件结构

### 1. 数据库表
- **文件**: `navigation.sql`
- **表名**: `fa_navigation`
- **字段说明**:
  - `id`: 主键ID
  - `title`: 导航标题
  - `icon`: 图标类名（支持FontAwesome图标）
  - `url`: 链接地址
  - `target`: 打开方式（_self当前窗口，_blank新窗口）
  - `sort`: 排序权重
  - `status`: 状态（normal正常，hidden隐藏）
  - `createtime`: 创建时间
  - `updatetime`: 更新时间

### 2. 模型文件
- **文件**: `application/common/model/Navigation.php`
- **功能**: 
  - 自动时间戳管理
  - 状态和打开方式的文本转换
  - 获取启用导航列表的静态方法

### 3. 后台控制器
- **文件**: `application/admin/controller/Navigation.php`
- **功能**: 
  - 继承Backend基类，提供完整的CRUD操作
  - 支持列表查看、添加、编辑、删除、批量操作

### 4. API控制器
- **文件**: `application/api/controller/Navigation.php`
- **接口说明**:
  - `GET /api/navigation/index`: 获取导航列表
  - `GET /api/navigation/detail?id=1`: 获取单个导航详情
  - `GET /api/navigation/count`: 获取导航数量

### 5. 后台视图文件
- **目录**: `application/admin/view/navigation/`
- **文件**:
  - `index.html`: 导航列表页面
  - `add.html`: 添加导航页面
  - `edit.html`: 编辑导航页面

### 6. 前端JavaScript
- **文件**: `public/assets/js/backend/navigation.js`
- **功能**: 
  - 表格初始化和事件绑定
  - 图标选择器功能
  - 表单验证

### 7. 语言包
- **文件**: `application/admin/lang/zh-cn/navigation.php`
- **功能**: 中文语言包，支持国际化

### 8. 验证器
- **文件**: `application/admin/validate/Navigation.php`
- **功能**: 表单数据验证规则

## 安装步骤

### 1. 导入数据库表
```sql
-- 执行 navigation.sql 文件中的SQL语句
```

### 2. 添加菜单权限
在后台管理系统中添加导航管理的菜单项：
- 菜单名称: 导航管理
- 控制器: navigation
- 图标: fa fa-list

### 3. 配置权限
为相应的管理员角色分配导航管理权限。

## 使用方法

### 后台管理
1. 登录后台管理系统
2. 找到"导航管理"菜单
3. 可以进行以下操作：
   - 查看导航列表
   - 添加新导航
   - 编辑现有导航
   - 删除导航
   - 批量设置状态

### API调用示例

#### 获取导航列表
```javascript
fetch('/api/navigation/index')
  .then(response => response.json())
  .then(data => {
    console.log(data.data); // 导航列表数据
  });
```

#### 获取单个导航
```javascript
fetch('/api/navigation/detail?id=1')
  .then(response => response.json())
  .then(data => {
    console.log(data.data); // 导航详情数据
  });
```

### 前端集成
在前端页面中可以通过API获取导航数据并渲染：

```javascript
// 获取并渲染导航
async function renderNavigation() {
  try {
    const response = await fetch('/api/navigation/index');
    const result = await response.json();
    
    if (result.code === 1) {
      const navHtml = result.data.map(nav => `
        <a href="${nav.url}" target="${nav.target}" class="nav-item">
          <i class="${nav.icon}"></i>
          ${nav.title}
        </a>
      `).join('');
      
      document.getElementById('navigation').innerHTML = navHtml;
    }
  } catch (error) {
    console.error('获取导航失败:', error);
  }
}
```

## 注意事项

1. **图标支持**: 默认支持FontAwesome图标，格式如 `fa fa-home`
2. **URL格式**: 支持相对路径和绝对路径
3. **排序**: 数值越小排序越靠前
4. **状态控制**: 只有状态为"正常"的导航才会在API中返回
5. **权限控制**: 后台操作需要相应的权限配置

## 扩展建议

1. **图标选择器**: 可以集成更完善的图标选择器插件
2. **多级导航**: 可以扩展支持父子级导航结构
3. **权限控制**: 可以为导航添加权限控制字段
4. **多语言**: 可以扩展支持多语言导航标题
5. **缓存优化**: 可以添加缓存机制提高API性能
