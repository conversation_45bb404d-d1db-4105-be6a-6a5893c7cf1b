<?php
// 测试API路由
echo "<h2>API路由测试</h2>";

$testUrls = [
    'http://127.0.0.3/api/posts',
    'http://127.0.0.3/api/posts/detail/4',
    'http://127.0.0.3/api/posts/detail?id=4',
    'http://127.0.0.3/api/posts/categories',
    'http://127.0.0.3/api/posts/builtinIcons'
];

foreach ($testUrls as $url) {
    echo "<h3>测试: {$url}</h3>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP状态码:</strong> {$httpCode}</p>";
    if ($error) {
        echo "<p><strong>错误:</strong> {$error}</p>";
    }
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data) {
            echo "<p><strong>响应:</strong> " . ($data['msg'] ?? '成功') . "</p>";
            if (isset($data['data']) && is_array($data['data'])) {
                if (isset($data['data']['data'])) {
                    echo "<p><strong>数据条数:</strong> " . count($data['data']['data']) . "</p>";
                } elseif (isset($data['data']['id'])) {
                    echo "<p><strong>帖子ID:</strong> " . $data['data']['id'] . "</p>";
                } else {
                    echo "<p><strong>数据类型:</strong> " . gettype($data['data']) . "</p>";
                }
            }
        } else {
            echo "<p><strong>响应内容:</strong> " . htmlspecialchars(substr($response, 0, 200)) . "</p>";
        }
    } else {
        echo "<p><strong>响应内容:</strong> " . htmlspecialchars(substr($response, 0, 200)) . "</p>";
    }
    
    echo "<hr>";
}
?>
