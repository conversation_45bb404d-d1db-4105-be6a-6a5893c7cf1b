<?php

namespace app\admin\validate;

use think\Validate;

class Navigation extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'title'  => 'require|length:1,100',
        'icon'   => 'require|length:1,100',
        'url'    => 'require|length:1,255',
        'target' => 'require|in:_self,_blank',
        'sort'   => 'integer|egt:0',
        'status' => 'require|in:normal,hidden',
    ];
    
    /**
     * 提示消息
     */
    protected $message = [
        'title.require'  => '导航标题不能为空',
        'title.length'   => '导航标题长度必须在1-100个字符之间',
        'icon.require'   => '图标不能为空',
        'icon.length'    => '图标长度必须在1-100个字符之间',
        'url.require'    => '链接地址不能为空',
        'url.length'     => '链接地址长度必须在1-255个字符之间',
        'target.require' => '打开方式不能为空',
        'target.in'      => '打开方式必须为当前窗口或新窗口',
        'sort.integer'   => '排序必须为整数',
        'sort.egt'       => '排序必须大于等于0',
        'status.require' => '状态不能为空',
        'status.in'      => '状态必须为正常或隐藏',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['title', 'icon', 'url', 'target', 'sort', 'status'],
        'edit' => ['title', 'icon', 'url', 'target', 'sort', 'status'],
    ];
}
