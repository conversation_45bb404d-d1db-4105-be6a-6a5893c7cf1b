-- 创建帖子分类表
CREATE TABLE IF NOT EXISTS `fa_post_categories` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子分类表';

-- 创建帖子表
CREATE TABLE IF NOT EXISTS `fa_posts` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '帖子ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '分类ID',
  `title` varchar(255) NOT NULL COMMENT '帖子标题',
  `content` longtext NOT NULL COMMENT '帖子内容',
  `post_type` varchar(50) DEFAULT 'normal' COMMENT '帖子类型：normal普通,sale出售,hot热门,expert高手',
  `icon_type` varchar(20) DEFAULT 'default' COMMENT '图标类型：default默认,upload上传,builtin内置',
  `icon_path` varchar(255) DEFAULT NULL COMMENT '图标路径',
  `views` int(10) unsigned DEFAULT '0' COMMENT '浏览次数',
  `replies` int(10) unsigned DEFAULT '0' COMMENT '回复数',
  `likes` int(10) unsigned DEFAULT '0' COMMENT '点赞数',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门',
  `status` varchar(20) DEFAULT 'published' COMMENT '状态：draft草稿,published已发布,hidden隐藏,deleted已删除',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_post_type` (`post_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子表';

-- 插入默认分类数据
INSERT INTO `fa_post_categories` (`name`, `description`, `sort_order`, `status`, `create_time`, `update_time`) VALUES
('综合讨论', '综合性话题讨论区', 1, 1, NOW(), NOW()),
('技术交流', '技术相关问题交流', 2, 1, NOW(), NOW()),
('买卖交易', '物品买卖交易区', 3, 1, NOW(), NOW()),
('新手求助', '新手问题求助区', 4, 1, NOW(), NOW()),
('经验分享', '经验心得分享区', 5, 1, NOW(), NOW());

-- 创建内置图标目录（如果不存在）
-- 这些图标文件需要手动上传到对应目录
