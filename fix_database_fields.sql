-- =====================================================
-- 检查并修复数据库字段名问题
-- =====================================================

-- 检查当前表结构
SHOW COLUMNS FROM fa_posts LIKE '%time%';
SHOW COLUMNS FROM fa_post_categories LIKE '%time%';

-- 如果表中还是使用create_time字段，需要重命名
-- 检查fa_posts表是否需要修改字段名
SET @posts_has_create_time = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'houtai' AND TABLE_NAME = 'fa_posts' AND COLUMN_NAME = 'create_time');

SET @posts_has_createtime = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'houtai' AND TABLE_NAME = 'fa_posts' AND COLUMN_NAME = 'createtime');

-- 如果存在create_time但不存在createtime，则重命名字段
SET @sql_posts = CASE 
    WHEN @posts_has_create_time > 0 AND @posts_has_createtime = 0 THEN 
        'ALTER TABLE fa_posts 
         CHANGE COLUMN create_time createtime int(10) DEFAULT NULL COMMENT "创建时间",
         CHANGE COLUMN update_time updatetime int(10) DEFAULT NULL COMMENT "更新时间";'
    ELSE 'SELECT "fa_posts表字段已正确" as result;'
END;

PREPARE stmt_posts FROM @sql_posts;
EXECUTE stmt_posts;
DEALLOCATE PREPARE stmt_posts;

-- 检查fa_post_categories表是否需要修改字段名
SET @categories_has_create_time = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'houtai' AND TABLE_NAME = 'fa_post_categories' AND COLUMN_NAME = 'create_time');

SET @categories_has_createtime = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'houtai' AND TABLE_NAME = 'fa_post_categories' AND COLUMN_NAME = 'createtime');

-- 如果存在create_time但不存在createtime，则重命名字段
SET @sql_categories = CASE 
    WHEN @categories_has_create_time > 0 AND @categories_has_createtime = 0 THEN 
        'ALTER TABLE fa_post_categories 
         CHANGE COLUMN create_time createtime int(10) DEFAULT NULL COMMENT "创建时间",
         CHANGE COLUMN update_time updatetime int(10) DEFAULT NULL COMMENT "更新时间";'
    ELSE 'SELECT "fa_post_categories表字段已正确" as result;'
END;

PREPARE stmt_categories FROM @sql_categories;
EXECUTE stmt_categories;
DEALLOCATE PREPARE stmt_categories;

-- 最终检查
SELECT '修复完成后的表结构:' as '状态';
SHOW COLUMNS FROM fa_posts LIKE '%time%';
SHOW COLUMNS FROM fa_post_categories LIKE '%time%';

-- 检查数据
SELECT '帖子数据检查:' as '状态';
SELECT id, title, post_type, createtime, updatetime FROM fa_posts LIMIT 3;
