define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'posts/index' + location.search,
                    add_url: 'posts/add',
                    edit_url: 'posts/edit',
                    del_url: 'posts/del',
                    multi_url: 'posts/multi',
                    import_url: 'posts/import',
                    table: 'posts',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'title', title: __('Title'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'user.username', title: __('Author'), operate: 'LIKE'},
                        {field: 'category.name', title: __('Category'), operate: 'LIKE'},
                        {field: 'post_type_text', title: __('Post Type'), operate: false, searchList: {"normal":__('Normal'),"sale":__('Sale'),"hot":__('Hot'),"expert":__('Expert')}},
                        {field: 'icon_url', title: __('Icon'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'views', title: __('Views'), operate: false},
                        {field: 'replies', title: __('Replies'), operate: false},
                        {field: 'likes', title: __('Likes'), operate: false},
                        {field: 'is_top', title: __('Is Top'), searchList: {"0":__('No'),"1":__('Yes')}, formatter: Table.api.formatter.toggle},
                        {field: 'is_hot', title: __('Is Hot'), searchList: {"0":__('No'),"1":__('Yes')}, formatter: Table.api.formatter.toggle},
                        {field: 'status_text', title: __('Status'), operate: false, searchList: {"published":__('Published'),"draft":__('Draft'),"hidden":__('Hidden'),"deleted":__('Deleted')}},
                        {field: 'createtime', title: __('Create time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 图标类型切换
                $(document).on('change', 'input[name="row[icon_type]"]', function() {
                    var iconType = $(this).val();
                    if (iconType === 'builtin') {
                        $('#builtin-icons').show();
                        $('#upload-icon').hide();
                    } else {
                        $('#builtin-icons').hide();
                        $('#upload-icon').show();
                    }
                });
                
                // 内置图标选择
                $(document).on('click', '.icon-item', function() {
                    $('.icon-item').removeClass('selected');
                    $(this).addClass('selected');
                    var iconKey = $(this).data('value');
                    $('input[name="row[selected_icon]"]').remove();
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'row[selected_icon]',
                        value: iconKey
                    }).appendTo('form');
                });
            }
        }
    };
    return Controller;
});
